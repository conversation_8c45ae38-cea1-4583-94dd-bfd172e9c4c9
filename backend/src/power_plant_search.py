#!/usr/bin/env python3
"""
Power Plant Annual Report Search Terminal Interface

This script provides a simple terminal interface to test the power plant
annual report search functionality without requiring the frontend.
"""

import os
import sys
import requests
from typing import Dict, Any, List
from langchain_core.messages import HumanMessage

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from agent.graph import power_plant_graph
from agent.configuration import Configuration
from agent.pdf_scraper import PDFScraper


def initialize_power_plant_state(plant_name: str) -> Dict[str, Any]:
    """Initialize the state for power plant annual report search.
    
    Args:
        plant_name: Name of the power plant to search for
        
    Returns:
        Dictionary containing the initial state for the search
    """
    return {
        "messages": [HumanMessage(content=plant_name)],
        "plant_name": plant_name,
        "search_phase": "direct_search",
        "search_query": [],
        "web_research_result": [],
        "sources_gathered": [],
        "annual_report_urls": [],
        "initial_search_query_count": 2,
        "max_research_loops": 2,
        "research_loop_count": 0,
        "reasoning_model": "gemini-2.5-flash",
        "holding_company_name": "",
        "found_annual_reports": False,
    }


def print_separator():
    """Print a visual separator for better output formatting."""
    print("\n" + "="*80 + "\n")


def print_search_results(final_state: Dict[str, Any]):
    """Print the final search results in a formatted way.

    Args:
        final_state: The final state from the graph execution
    """
    print_separator()
    print("🔍 POWER PLANT ANNUAL REPORT SEARCH RESULTS")
    print_separator()

    plant_name = final_state.get("plant_name", "Unknown")
    print(f"Power Plant: {plant_name}")

    holding_company = final_state.get("holding_company_name", "")
    if holding_company:
        print(f"Holding Company: {holding_company}")

    search_phase = final_state.get("search_phase", "direct_search")
    print(f"Search Phase: {search_phase.replace('_', ' ').title()}")

    print_separator()

    # Print the final answer
    if final_state.get("messages"):
        final_message = final_state["messages"][-1]
        if hasattr(final_message, 'content'):
            print("📄 ANNUAL REPORT FINDINGS:")
            print(final_message.content)

    print_separator()

    # Print sources found
    sources = final_state.get("sources_gathered", [])
    if sources:
        print("🔗 SOURCES FOUND:")
        for i, source in enumerate(sources, 1):
            print(f"{i}. {source.get('label', 'Unknown Source')}")
            print(f"   URL: {source.get('value', 'No URL')}")
            print()

        # Ask if user wants to scrape PDFs
        print_separator()
        scrape_pdfs = input("🤖 Would you like to scrape PDFs from these sources? (y/n): ").strip().lower()

        if scrape_pdfs in ['y', 'yes']:
            scrape_annual_report_pdfs(sources, plant_name, final_state)
    else:
        print("❌ No sources found")

    print_separator()


def scrape_annual_report_pdfs(sources: List[Dict[str, Any]], plant_name: str, final_state: Dict[str, Any] = None):
    """Scrape PDF annual reports from the identified sources.

    Args:
        sources: List of source dictionaries with URLs
        plant_name: Name of the power plant
        final_state: Final state containing the AI's answer with URLs
    """
    print("🤖 Starting PDF scraping process...")
    print("⚠️  Note: This requires Chrome browser to be installed.")

    try:
        # Extract URLs from sources
        source_urls = [source.get('value', '') for source in sources if source.get('value')]
        source_urls = [url for url in source_urls if url.startswith('http')]

        # ALSO extract URLs from the final answer (this is often more accurate)
        answer_urls = []
        if final_state and final_state.get("messages"):
            final_message = final_state["messages"][-1]
            if hasattr(final_message, 'content'):
                answer = final_message.content

                # Extract URLs from the answer using regex
                import re
                found_urls = re.findall(r'https?://[^\s\)\]]+', answer)

                # Clean up URLs (remove trailing punctuation)
                clean_urls = []
                for url in found_urls:
                    # Remove trailing punctuation
                    url = url.rstrip('.,;:!?)')
                    if url.startswith('http'):
                        clean_urls.append(url)
                        print(f"   📄 Found URL in answer: {url}")

                answer_urls = clean_urls

        # Combine source URLs and answer URLs
        all_urls = source_urls + answer_urls
        all_urls = [url for url in all_urls if url.startswith('http')]

        if not all_urls:
            print("❌ No valid URLs found for scraping.")
            return

        print(f"📊 Found {len(source_urls)} source URLs and {len(answer_urls)} answer URLs")

        # Deduplicate URLs by resolving redirects and comparing final URLs
        print("🔍 Deduplicating and resolving URLs...")
        unique_urls = []
        seen_domains = set()

        for i, url in enumerate(all_urls, 1):
            try:
                print(f"   Processing URL {i}/{len(all_urls)}: {url[:60]}...")

                # For redirect URLs, resolve to get the actual URL
                if "grounding-api-redirect" in url:
                    # Try to resolve to get the actual URL
                    response = requests.head(url, allow_redirects=True, timeout=10)
                    resolved_url = response.url
                    domain = resolved_url.split('/')[2] if '/' in resolved_url else resolved_url
                    print(f"      Resolved to: {resolved_url}")
                else:
                    domain = url.split('/')[2] if '/' in url else url
                    resolved_url = url
                    print(f"      Direct URL: {resolved_url}")

                # Only add if we haven't seen this domain before
                if domain not in seen_domains:
                    unique_urls.append(resolved_url)
                    seen_domains.add(domain)
                    print(f"   ✅ Added: {domain}")
                else:
                    print(f"   ⏭️  Skipped duplicate: {domain}")

            except Exception as e:
                print(f"   ❌ Could not resolve URL: {url[:50]}... ({str(e)})")
                # If resolution fails, try to use the original URL
                if "grounding-api-redirect" not in url:
                    unique_urls.append(url)
                    print(f"   ⚠️  Using original URL as fallback")
                continue

        if not unique_urls:
            print("❌ No unique URLs found after deduplication.")
            return

        print(f"\n📊 Will scrape {len(unique_urls)} unique URL(s)")

        # Initialize PDF scraper
        scraper = PDFScraper(download_dir="./annual_reports", headless=True)

        # Scrape PDFs from unique URLs only
        downloaded_files = scraper.scrape_annual_reports(unique_urls, plant_name)

        # If no PDFs found and we have answer URLs, try some common annual report URL patterns
        if not downloaded_files and answer_urls:
            print("\n🔄 No PDFs found on resolved URLs, trying alternative URL patterns...")

            alternative_urls = []
            for url in answer_urls:
                if 'sanmiguel.com.ph' in url:
                    # Try common San Miguel annual report URL patterns
                    alternatives = [
                        "https://www.sanmiguel.com.ph/investor-relations/annual-reports",
                        "https://www.sanmiguel.com.ph/corporate/investor-relations/annual-reports",
                        "https://www.smc.com.ph/investor-relations/annual-reports/"
                    ]
                    alternative_urls.extend(alternatives)
                # Add more patterns for other companies as needed

            if alternative_urls:
                print(f"   Trying {len(alternative_urls)} alternative URLs...")
                for alt_url in alternative_urls:
                    if alt_url not in unique_urls:  # Don't repeat URLs we already tried
                        print(f"   🔍 Trying: {alt_url}")
                        try:
                            alt_pdfs = scraper.find_pdf_links(alt_url)
                            if alt_pdfs:
                                print(f"   ✅ Found {len(alt_pdfs)} PDFs on alternative URL!")
                                for pdf_info in alt_pdfs:
                                    file_path = scraper.download_pdf(pdf_info, plant_name)
                                    if file_path:
                                        downloaded_files.append(file_path)
                                break  # Stop after finding PDFs
                        except Exception as e:
                            print(f"   ❌ Failed: {str(e)}")
                            continue

        if downloaded_files:
            # Remove duplicates from downloaded files
            unique_files = list(set(downloaded_files))
            print(f"\n✅ Successfully downloaded {len(unique_files)} unique PDF(s):")
            for file_path in unique_files:
                print(f"   📄 {file_path}")
            print(f"\n📁 Files saved in: ./annual_reports/{plant_name.replace(' ', '_')}/")
        else:
            print("\n❌ No PDF files were downloaded.")
            print("   This could be because:")
            print("   - No annual report PDFs were found on the pages")
            print("   - The pages require authentication")
            print("   - The PDFs are embedded or dynamically loaded")

    except ImportError:
        print("❌ Selenium not installed. To enable PDF scraping, install selenium:")
        print("   pip install selenium")
        print("   Also ensure Chrome browser is installed.")

    except Exception as e:
        print(f"❌ Error during PDF scraping: {str(e)}")
        print("   Make sure Chrome browser is installed and accessible.")


def main():
    """Main function to run the power plant search interface."""
    print("🏭 Power Plant Annual Report Search Engine")
    print("==========================================")
    print()
    print("This tool searches for annual reports for power plants.")
    print("It will first search directly for the power plant's annual reports,")
    print("and if not found, will search for the holding company's reports.")
    print()
    
    while True:
        try:
            # Get power plant name from user
            plant_name = input("Enter power plant name (or 'quit' to exit): ").strip()
            
            if plant_name.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not plant_name:
                print("❌ Please enter a valid power plant name.")
                continue
            
            print(f"\n🔍 Searching for annual reports for: {plant_name}")
            print("⏳ This may take a few moments...")
            
            # Initialize state
            initial_state = initialize_power_plant_state(plant_name)
            
            # Run the power plant search graph
            try:
                final_state = power_plant_graph.invoke(initial_state)
                print_search_results(final_state)
                
            except Exception as e:
                print(f"\n❌ Error during search: {str(e)}")
                print("Please check your API keys and internet connection.")
                continue
            
            # Ask if user wants to search for another plant
            print("\n" + "-"*50)
            continue_search = input("Search for another power plant? (y/n): ").strip().lower()
            if continue_search not in ['y', 'yes']:
                print("👋 Goodbye!")
                break
                
        except KeyboardInterrupt:
            print("\n\n👋 Search interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Unexpected error: {str(e)}")
            continue


if __name__ == "__main__":
    # Check for required environment variables
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        print("Please set your Gemini API key before running this script.")
        print("Example: export GEMINI_API_KEY='your-api-key-here'")
        sys.exit(1)
    
    main()
