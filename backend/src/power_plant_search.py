#!/usr/bin/env python3
"""
Power Plant Annual Report Search Terminal Interface

This script provides a simple terminal interface to test the power plant
annual report search functionality without requiring the frontend.
"""

import os
import sys
from typing import Dict, Any, List
from langchain_core.messages import HumanMessage

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from agent.graph import power_plant_graph
from agent.configuration import Configuration
from agent.pdf_scraper import PDFScraper


def initialize_power_plant_state(plant_name: str) -> Dict[str, Any]:
    """Initialize the state for power plant annual report search.
    
    Args:
        plant_name: Name of the power plant to search for
        
    Returns:
        Dictionary containing the initial state for the search
    """
    return {
        "messages": [HumanMessage(content=plant_name)],
        "plant_name": plant_name,
        "search_phase": "direct_search",
        "search_query": [],
        "web_research_result": [],
        "sources_gathered": [],
        "annual_report_urls": [],
        "initial_search_query_count": 2,
        "max_research_loops": 2,
        "research_loop_count": 0,
        "reasoning_model": "gemini-2.5-flash",
        "holding_company_name": "",
        "found_annual_reports": False,
    }


def print_separator():
    """Print a visual separator for better output formatting."""
    print("\n" + "="*80 + "\n")


def print_search_results(final_state: Dict[str, Any]):
    """Print the final search results in a formatted way.

    Args:
        final_state: The final state from the graph execution
    """
    print_separator()
    print("🔍 POWER PLANT ANNUAL REPORT SEARCH RESULTS")
    print_separator()

    plant_name = final_state.get("plant_name", "Unknown")
    print(f"Power Plant: {plant_name}")

    holding_company = final_state.get("holding_company_name", "")
    if holding_company:
        print(f"Holding Company: {holding_company}")

    search_phase = final_state.get("search_phase", "direct_search")
    print(f"Search Phase: {search_phase.replace('_', ' ').title()}")

    print_separator()

    # Print the final answer
    if final_state.get("messages"):
        final_message = final_state["messages"][-1]
        if hasattr(final_message, 'content'):
            print("📄 ANNUAL REPORT FINDINGS:")
            print(final_message.content)

    print_separator()

    # Print sources found
    sources = final_state.get("sources_gathered", [])
    if sources:
        print("🔗 SOURCES FOUND:")
        for i, source in enumerate(sources, 1):
            print(f"{i}. {source.get('label', 'Unknown Source')}")
            print(f"   URL: {source.get('value', 'No URL')}")
            print()

        # Ask if user wants to scrape PDFs
        print_separator()
        scrape_pdfs = input("🤖 Would you like to scrape PDFs from these sources? (y/n): ").strip().lower()

        if scrape_pdfs in ['y', 'yes']:
            scrape_annual_report_pdfs(sources, plant_name)
    else:
        print("❌ No sources found")

    print_separator()


def scrape_annual_report_pdfs(sources: List[Dict[str, Any]], plant_name: str):
    """Scrape PDF annual reports from the identified sources.

    Args:
        sources: List of source dictionaries with URLs
        plant_name: Name of the power plant
    """
    print("🤖 Starting PDF scraping process...")
    print("⚠️  Note: This requires Chrome browser to be installed.")

    try:
        # Extract URLs from sources
        urls = [source.get('value', '') for source in sources if source.get('value')]
        urls = [url for url in urls if url.startswith('http')]

        if not urls:
            print("❌ No valid URLs found for scraping.")
            return

        # Initialize PDF scraper
        scraper = PDFScraper(download_dir="./annual_reports", headless=True)

        # Scrape PDFs
        downloaded_files = scraper.scrape_annual_reports(urls, plant_name)

        if downloaded_files:
            print(f"\n✅ Successfully downloaded {len(downloaded_files)} PDF(s):")
            for file_path in downloaded_files:
                print(f"   📄 {file_path}")
            print(f"\n📁 Files saved in: ./annual_reports/{plant_name.replace(' ', '_')}/")
        else:
            print("\n❌ No PDF files were downloaded.")
            print("   This could be because:")
            print("   - No annual report PDFs were found on the pages")
            print("   - The pages require authentication")
            print("   - The PDFs are embedded or dynamically loaded")

    except ImportError:
        print("❌ Selenium not installed. To enable PDF scraping, install selenium:")
        print("   pip install selenium")
        print("   Also ensure Chrome browser is installed.")

    except Exception as e:
        print(f"❌ Error during PDF scraping: {str(e)}")
        print("   Make sure Chrome browser is installed and accessible.")


def main():
    """Main function to run the power plant search interface."""
    print("🏭 Power Plant Annual Report Search Engine")
    print("==========================================")
    print()
    print("This tool searches for annual reports for power plants.")
    print("It will first search directly for the power plant's annual reports,")
    print("and if not found, will search for the holding company's reports.")
    print()
    
    while True:
        try:
            # Get power plant name from user
            plant_name = input("Enter power plant name (or 'quit' to exit): ").strip()
            
            if plant_name.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not plant_name:
                print("❌ Please enter a valid power plant name.")
                continue
            
            print(f"\n🔍 Searching for annual reports for: {plant_name}")
            print("⏳ This may take a few moments...")
            
            # Initialize state
            initial_state = initialize_power_plant_state(plant_name)
            
            # Run the power plant search graph
            try:
                final_state = power_plant_graph.invoke(initial_state)
                print_search_results(final_state)
                
            except Exception as e:
                print(f"\n❌ Error during search: {str(e)}")
                print("Please check your API keys and internet connection.")
                continue
            
            # Ask if user wants to search for another plant
            print("\n" + "-"*50)
            continue_search = input("Search for another power plant? (y/n): ").strip().lower()
            if continue_search not in ['y', 'yes']:
                print("👋 Goodbye!")
                break
                
        except KeyboardInterrupt:
            print("\n\n👋 Search interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Unexpected error: {str(e)}")
            continue


if __name__ == "__main__":
    # Check for required environment variables
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        print("Please set your Gemini API key before running this script.")
        print("Example: export GEMINI_API_KEY='your-api-key-here'")
        sys.exit(1)
    
    main()
