import os

from agent.tools_and_schemas import SearchQueryList, Reflection, PowerPlantReflection
from dotenv import load_dotenv
from langchain_core.messages import AIMessage
from langgraph.types import Send
from langgraph.graph import StateGraph
from langgraph.graph import START, END
from langchain_core.runnables import RunnableConfig
from google.genai import Client

from agent.state import (
    OverallState,
    QueryGenerationState,
    ReflectionState,
    WebSearchState,
)
from agent.configuration import Configuration
from agent.prompts import (
    get_current_date,
    query_writer_instructions,
    web_searcher_instructions,
    reflection_instructions,
    answer_instructions,
    power_plant_query_instructions,
    holding_company_search_instructions,
    power_plant_web_search_instructions,
    power_plant_reflection_instructions,
    power_plant_final_answer_instructions,
)
from langchain_google_genai import ChatGoogleGenerativeAI
from agent.utils import (
    get_citations,
    get_research_topic,
    insert_citation_markers,
    resolve_urls,
)

load_dotenv()

if os.getenv("GEMINI_API_KEY") is None:
    raise ValueError("GEMINI_API_KEY is not set")

# Used for Google Search API
genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))


# Helper function for aggressive source filtering
def filter_sources_aggressively(sources, plant_name=""):
    """Apply ultra-aggressive filtering to sources to return only the most relevant ones.

    Args:
        sources: List of source dictionaries
        plant_name: Name of the power plant being searched

    Returns:
        Filtered list of sources (maximum 1-2 sources)
    """
    if not sources:
        return sources

    # Determine expected company indicators based on plant name
    plant_lower = plant_name.lower()

    # Map common plant names to their company indicators
    company_indicators = []
    if "seil" in plant_lower:
        company_indicators = ["seilenergy"]
    elif "duke" in plant_lower:
        company_indicators = ["duke-energy", "duke"]
    elif "exelon" in plant_lower:
        company_indicators = ["exelon"]
    elif "southern" in plant_lower:
        company_indicators = ["southerncompany", "southern"]
    elif "nextera" in plant_lower:
        company_indicators = ["nexteraenergy", "nextera"]
    else:
        # Generic filtering for unknown plants
        company_indicators = [plant_lower.replace(" ", "").replace("-", "")]

    # Third-party sites to always exclude
    third_party_blacklist = [
        'tracxn', 'zaubacorp', 'careratings', 'pitchbook', 'crisilratings',
        'icra', 'crisil', 'moodys', 'fitch', 'sp', 'standardandpoors',
        'bloomberg', 'reuters', 'yahoo', 'google', 'wikipedia'
    ]

    # Filter out third-party sources
    company_sources = []
    for source in sources:
        label = source.get("label", "").lower()
        url = source.get("value", "").lower()

        # Skip third-party sites
        if any(blacklisted in label or blacklisted in url for blacklisted in third_party_blacklist):
            continue

        # Keep sources that match company indicators
        if company_indicators and any(indicator in label for indicator in company_indicators):
            company_sources.append(source)
        elif not company_indicators:  # Fallback for unknown companies
            company_sources.append(source)

    # If no company sources found, take the first non-third-party source
    if not company_sources:
        for source in sources:
            label = source.get("label", "").lower()
            url = source.get("value", "").lower()
            if not any(blacklisted in label or blacklisted in url for blacklisted in third_party_blacklist):
                company_sources = [source]
                break

    # Deduplicate by label and return only the best one
    seen_labels = set()
    final_sources = []

    for source in company_sources:
        label = source.get("label", "")
        if label not in seen_labels:
            final_sources.append(source)
            seen_labels.add(label)

            # Stop at 1 source for maximum focus
            if len(final_sources) >= 1:
                break

    return final_sources


# Power Plant Specific Nodes
def generate_power_plant_query(state: OverallState, config: RunnableConfig) -> QueryGenerationState:
    """LangGraph node that generates search queries for power plant annual reports.

    Uses Gemini 2.0 Flash to create optimized search queries specifically for finding
    power plant annual reports, with logic for direct search vs holding company search.

    Args:
        state: Current graph state containing the power plant name and search phase
        config: Configuration for the runnable, including LLM provider settings

    Returns:
        Dictionary with state update, including search_query key containing the generated queries
    """
    configurable = Configuration.from_runnable_config(config)

    # Extract plant name from the user message
    plant_name = state.get("plant_name", "")
    if not plant_name:
        # Extract from the first message if not set
        if state.get("messages"):
            plant_name = state["messages"][0].content.strip()
            state["plant_name"] = plant_name

    # Determine search phase
    search_phase = state.get("search_phase", "direct_search")

    # Initialize LLM
    llm = ChatGoogleGenerativeAI(
        model=configurable.query_generator_model,
        temperature=1.0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    structured_llm = llm.with_structured_output(SearchQueryList)

    # Choose appropriate prompt based on search phase
    if search_phase == "direct_search":
        formatted_prompt = power_plant_query_instructions.format(
            current_date=get_current_date(),
            plant_name=plant_name,
        )
    else:  # holding_company_search
        formatted_prompt = holding_company_search_instructions.format(
            current_date=get_current_date(),
            plant_name=plant_name,
        )

    # Generate the search queries
    result = structured_llm.invoke(formatted_prompt)
    return {"search_query": result.query}


# Original Nodes (kept for reference but not used in power plant workflow)
def generate_query(state: OverallState, config: RunnableConfig) -> QueryGenerationState:
    """LangGraph node that generates search queries based on the User's question.

    Uses Gemini 2.0 Flash to create an optimized search queries for web research based on
    the User's question.

    Args:
        state: Current graph state containing the User's question
        config: Configuration for the runnable, including LLM provider settings

    Returns:
        Dictionary with state update, including search_query key containing the generated queries
    """
    configurable = Configuration.from_runnable_config(config)

    # check for custom initial search query count
    if state.get("initial_search_query_count") is None:
        state["initial_search_query_count"] = configurable.number_of_initial_queries

    # init Gemini 2.0 Flash
    llm = ChatGoogleGenerativeAI(
        model=configurable.query_generator_model,
        temperature=1.0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    structured_llm = llm.with_structured_output(SearchQueryList)

    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = query_writer_instructions.format(
        current_date=current_date,
        research_topic=get_research_topic(state["messages"]),
        number_queries=state["initial_search_query_count"],
    )
    # Generate the search queries
    result = structured_llm.invoke(formatted_prompt)
    return {"search_query": result.query}


def continue_to_power_plant_web_research(state: QueryGenerationState):
    """LangGraph node that sends power plant search queries to the web research node.

    This is used to spawn n number of web research nodes, one for each search query.
    """
    return [
        Send("power_plant_web_research", {
            "search_query": search_query,
            "id": int(idx),
            "search_phase": state.get("search_phase", "direct_search")
        })
        for idx, search_query in enumerate(state["search_query"])
    ]


def continue_to_web_research(state: QueryGenerationState):
    """LangGraph node that sends the search queries to the web research node.

    This is used to spawn n number of web research nodes, one for each search query.
    """
    return [
        Send("web_research", {"search_query": search_query, "id": int(idx)})
        for idx, search_query in enumerate(state["search_query"])
    ]


def power_plant_web_research(state: WebSearchState, config: RunnableConfig) -> OverallState:
    """LangGraph node that performs web research for power plant annual reports.

    Executes web search specifically focused on finding annual reports and investor
    relations pages for power plants or their holding companies.

    Args:
        state: Current graph state containing the search query and research context
        config: Configuration for the runnable, including search API settings

    Returns:
        Dictionary with state update, including sources_gathered and web_research_results
    """
    # Configure
    configurable = Configuration.from_runnable_config(config)

    # Get context from state - need to extract from search_query since WebSearchState is limited
    search_query = state.get("search_query", "")
    search_phase = state.get("search_phase", "direct_search")

    # Extract plant name from search query if not directly available
    plant_name = search_query  # The search query contains the plant-related search

    formatted_prompt = power_plant_web_search_instructions.format(
        current_date=get_current_date(),
        plant_name=plant_name,
        search_phase=search_phase,
    )

    # Uses the google genai client as the langchain client doesn't return grounding metadata
    response = genai_client.models.generate_content(
        model=configurable.query_generator_model,
        contents=formatted_prompt,
        config={
            "tools": [{"google_search": {}}],
            "temperature": 0,
        },
    )

    # resolve the urls to short urls for saving tokens and time
    resolved_urls = resolve_urls(
        response.candidates[0].grounding_metadata.grounding_chunks, state["id"]
    )

    # get citations from the response
    citations = get_citations(response, resolved_urls)

    # insert citation markers into the response text
    response_with_citations = insert_citation_markers(
        response.candidates[0].content.parts[0].text, citations
    )

    # Apply aggressive filtering right here at the source
    raw_sources = [
        {
            "label": segment["label"],
            "short_url": segment["short_url"],
            "value": segment["value"],
        }
        for citation in citations
        for segment in citation["segments"]
    ]

    # Filter sources immediately to prevent accumulation of irrelevant sources
    filtered_sources = filter_sources_aggressively(raw_sources, state.get("plant_name", ""))

    return {
        "sources_gathered": filtered_sources,
        "web_research_result": [response_with_citations],
    }


def web_research(state: WebSearchState, config: RunnableConfig) -> OverallState:
    """LangGraph node that performs web research using the native Google Search API tool.

    Executes a web search using the native Google Search API tool in combination with Gemini 2.0 Flash.

    Args:
        state: Current graph state containing the search query and research loop count
        config: Configuration for the runnable, including search API settings

    Returns:
        Dictionary with state update, including sources_gathered, research_loop_count, and web_research_results
    """
    # Configure
    configurable = Configuration.from_runnable_config(config)
    formatted_prompt = web_searcher_instructions.format(
        current_date=get_current_date(),
        research_topic=state["search_query"],
    )

    # Uses the google genai client as the langchain client doesn't return grounding metadata
    response = genai_client.models.generate_content(
        model=configurable.query_generator_model,
        contents=formatted_prompt,
        config={
            "tools": [{"google_search": {}}],
            "temperature": 0,
        },
    )
    # resolve the urls to short urls for saving tokens and time
    resolved_urls = resolve_urls(
        response.candidates[0].grounding_metadata.grounding_chunks, state["id"]
    )
    # Gets the citations and adds them to the generated text
    citations = get_citations(response, resolved_urls)
    modified_text = insert_citation_markers(response.text, citations)
    sources_gathered = [item for citation in citations for item in citation["segments"]]

    return {
        "sources_gathered": sources_gathered,
        "search_query": [state["search_query"]],
        "web_research_result": [modified_text],
    }


def power_plant_reflection(state: OverallState, config: RunnableConfig) -> ReflectionState:
    """LangGraph node that analyzes power plant annual report search results.

    Determines if sufficient annual report information has been found, or if we need
    to switch to searching for the holding company's annual reports.

    Args:
        state: Current graph state containing search results and power plant context
        config: Configuration for the runnable, including LLM provider settings

    Returns:
        Dictionary with state update for reflection results and next search phase
    """
    configurable = Configuration.from_runnable_config(config)
    state["research_loop_count"] = state.get("research_loop_count", 0) + 1
    reasoning_model = state.get("reasoning_model", configurable.reflection_model)

    # Get power plant context
    plant_name = state.get("plant_name", "")
    search_phase = state.get("search_phase", "direct_search")

    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = power_plant_reflection_instructions.format(
        current_date=current_date,
        plant_name=plant_name,
        search_phase=search_phase,
        summaries="\n\n---\n\n".join(state["web_research_result"]),
    )

    # Initialize LLM
    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    structured_llm = llm.with_structured_output(PowerPlantReflection)

    # Generate reflection
    result = structured_llm.invoke(formatted_prompt)

    # Update state based on reflection results
    update_dict = {
        "is_sufficient": result.is_sufficient,
        "knowledge_gap": result.knowledge_gap,
        "follow_up_queries": result.follow_up_queries,
        "search_phase": result.search_phase,
        "number_of_ran_queries": len(state.get("search_query", [])),
    }

    # If holding company was identified, update state
    if result.holding_company_name:
        update_dict["holding_company_name"] = result.holding_company_name

    return update_dict


def reflection(state: OverallState, config: RunnableConfig) -> ReflectionState:
    """LangGraph node that identifies knowledge gaps and generates potential follow-up queries.

    Analyzes the current summary to identify areas for further research and generates
    potential follow-up queries. Uses structured output to extract
    the follow-up query in JSON format.

    Args:
        state: Current graph state containing the running summary and research topic
        config: Configuration for the runnable, including LLM provider settings

    Returns:
        Dictionary with state update, including search_query key containing the generated follow-up query
    """
    configurable = Configuration.from_runnable_config(config)
    # Increment the research loop count and get the reasoning model
    state["research_loop_count"] = state.get("research_loop_count", 0) + 1
    reasoning_model = state.get("reasoning_model", configurable.reflection_model)

    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = reflection_instructions.format(
        current_date=current_date,
        research_topic=get_research_topic(state["messages"]),
        summaries="\n\n---\n\n".join(state["web_research_result"]),
    )
    # init Reasoning Model
    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=1.0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    result = llm.with_structured_output(Reflection).invoke(formatted_prompt)

    return {
        "is_sufficient": result.is_sufficient,
        "knowledge_gap": result.knowledge_gap,
        "follow_up_queries": result.follow_up_queries,
        "research_loop_count": state["research_loop_count"],
        "number_of_ran_queries": len(state["search_query"]),
    }


def evaluate_power_plant_research(
    state: ReflectionState,
    config: RunnableConfig,
) -> OverallState:
    """LangGraph routing function for power plant annual report search flow.

    Determines whether to continue searching, switch to holding company search,
    or finalize the results based on what has been found.

    Args:
        state: Current reflection state with search results analysis
        config: Configuration for the runnable

    Returns:
        String indicating next node or Send objects for continued search
    """
    configurable = Configuration.from_runnable_config(config)
    max_research_loops = (
        state.get("max_research_loops")
        if state.get("max_research_loops") is not None
        else configurable.max_research_loops
    )

    # If sufficient annual reports found or max loops reached, finalize
    if state["is_sufficient"] or state["research_loop_count"] >= max_research_loops:
        return "power_plant_finalize_answer"

    # If we were doing direct search and didn't find reports, try holding company search
    current_phase = state.get("search_phase", "direct_search")
    if current_phase == "direct_search" and not state["is_sufficient"]:
        # Switch to holding company search
        return [
            Send(
                "power_plant_web_research",
                {
                    "search_query": follow_up_query,
                    "id": state["number_of_ran_queries"] + int(idx),
                    "search_phase": "holding_company_search",
                },
            )
            for idx, follow_up_query in enumerate(state["follow_up_queries"])
        ]

    # Continue with follow-up queries in the same phase
    return [
        Send(
            "power_plant_web_research",
            {
                "search_query": follow_up_query,
                "id": state["number_of_ran_queries"] + int(idx),
                "search_phase": current_phase,
            },
        )
        for idx, follow_up_query in enumerate(state["follow_up_queries"])
    ]


def evaluate_research(
    state: ReflectionState,
    config: RunnableConfig,
) -> OverallState:
    """LangGraph routing function that determines the next step in the research flow.

    Controls the research loop by deciding whether to continue gathering information
    or to finalize the summary based on the configured maximum number of research loops.

    Args:
        state: Current graph state containing the research loop count
        config: Configuration for the runnable, including max_research_loops setting

    Returns:
        String literal indicating the next node to visit ("web_research" or "finalize_summary")
    """
    configurable = Configuration.from_runnable_config(config)
    max_research_loops = (
        state.get("max_research_loops")
        if state.get("max_research_loops") is not None
        else configurable.max_research_loops
    )
    if state["is_sufficient"] or state["research_loop_count"] >= max_research_loops:
        return "finalize_answer"
    else:
        return [
            Send(
                "web_research",
                {
                    "search_query": follow_up_query,
                    "id": state["number_of_ran_queries"] + int(idx),
                },
            )
            for idx, follow_up_query in enumerate(state["follow_up_queries"])
        ]


def power_plant_finalize_answer(state: OverallState, config: RunnableConfig):
    """LangGraph node that finalizes the power plant annual report search results.

    Prepares the final output with specific website URLs where annual report PDFs
    can be found for the power plant or its holding company.

    Args:
        state: Current graph state containing search results and power plant context
        config: Configuration for the runnable

    Returns:
        Dictionary with final formatted answer containing annual report URLs
    """
    configurable = Configuration.from_runnable_config(config)
    reasoning_model = state.get("reasoning_model") or configurable.answer_model

    # Get power plant context
    plant_name = state.get("plant_name", "")

    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = power_plant_final_answer_instructions.format(
        current_date=current_date,
        plant_name=plant_name,
        summaries="\n---\n\n".join(state["web_research_result"]),
    )

    # Initialize LLM
    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )

    # Generate final answer
    final_answer = llm.invoke(formatted_prompt)

    # Aggressive filtering to return only the most relevant sources
    def get_source_priority(source):
        """Calculate priority score for a source based on its relevance to annual reports."""
        url = source.get("value", "").lower()
        label = source.get("label", "").lower()

        # Very high priority - exact annual report pages
        very_high_priority = ['annualreportinv', 'annual-report', 'annual_report', 'investor-relations/annual']

        # High priority keywords (investor relations, annual reports)
        high_priority = ['investor-relations', 'annual-reports', 'investor_relations', 'annual_reports',
                        'financials', 'sec-filings', 'financial-statements', 'investors/annual']

        # Medium priority keywords
        medium_priority = ['investor', 'annual', 'financial', 'report']

        # Low priority (general company pages)
        low_priority = ['about', 'contact', 'news', 'careers', 'products', 'tracxn', 'zaubacorp']

        score = 0

        # Check for very high priority patterns
        for keyword in very_high_priority:
            if keyword in url:
                score += 20

        # Check for high priority patterns in URL path
        for keyword in high_priority:
            if keyword in url:
                score += 10

        # Check for medium priority patterns
        for keyword in medium_priority:
            if keyword in url or keyword in label:
                score += 3

        # Heavily penalize low priority pages and third-party sites
        for keyword in low_priority:
            if keyword in url or keyword in label:
                score -= 10

        # Bonus for clean, readable URLs (not encoded/API URLs)
        if len(url.split('/')) <= 6 and not any(char in url for char in ['%', '?', '&', '=']):
            score += 2

        return score

    # Get all sources and calculate their priority scores
    all_sources = state.get("sources_gathered", [])

    # VERY aggressive filtering - only keep the most relevant sources
    relevant_sources = []
    for source in all_sources:
        url = source.get("value", "").lower()
        label = source.get("label", "").lower()

        # Skip ALL third-party sites and rating agencies
        third_party_sites = ['tracxn', 'zaubacorp', 'careratings', 'pitchbook', 'crisilratings',
                           'icra', 'crisil', 'moodys', 'fitch', 'sp', 'standardandpoors']

        if any(site in url or site in label for site in third_party_sites):
            continue

        # Only keep sources from the actual company (very strict)
        company_indicators = ['seilenergy']  # Only the main company site

        if any(keyword in label for keyword in company_indicators):
            relevant_sources.append(source)

    # If no relevant sources found, take the first few from all sources as fallback
    if not relevant_sources:
        relevant_sources = all_sources[:2]

    # Score the relevant sources
    scored_sources = [(source, get_source_priority(source)) for source in relevant_sources]

    # Sort by priority score (highest first)
    scored_sources.sort(key=lambda x: x[1], reverse=True)

    # ULTRA aggressive deduplication - only keep the absolute best
    seen_labels = set()
    filtered_sources = []

    for source, score in scored_sources:
        label = source.get("label", "")

        # Skip if we've already seen this label/domain
        if label in seen_labels:
            continue

        # Only include sources with positive scores
        if score > 0:
            filtered_sources.append(source)
            seen_labels.add(label)

        # Stop at just 1 source for maximum focus
        if len(filtered_sources) >= 1:
            break

    # If no high-scoring sources, take the best one anyway
    if not filtered_sources and scored_sources:
        filtered_sources = [scored_sources[0][0]]

    formatted_sources = "\n\n## Key Sources:\n"
    for source in filtered_sources:
        formatted_sources += f"- [{source['label']}]({source['short_url']})\n"

    # Combine answer with sources
    complete_answer = final_answer.content + formatted_sources

    return {
        "messages": [AIMessage(content=complete_answer)],
        "sources_gathered": filtered_sources,  # Return only filtered sources
        "found_annual_reports": True,
    }


def finalize_answer(state: OverallState, config: RunnableConfig):
    """LangGraph node that finalizes the research summary.

    Prepares the final output by deduplicating and formatting sources, then
    combining them with the running summary to create a well-structured
    research report with proper citations.

    Args:
        state: Current graph state containing the running summary and sources gathered

    Returns:
        Dictionary with state update, including running_summary key containing the formatted final summary with sources
    """
    configurable = Configuration.from_runnable_config(config)
    reasoning_model = state.get("reasoning_model") or configurable.answer_model

    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = answer_instructions.format(
        current_date=current_date,
        research_topic=get_research_topic(state["messages"]),
        summaries="\n---\n\n".join(state["web_research_result"]),
    )

    # init Reasoning Model, default to Gemini 2.5 Flash
    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    result = llm.invoke(formatted_prompt)

    # Replace the short urls with the original urls and add all used urls to the sources_gathered
    unique_sources = []
    for source in state["sources_gathered"]:
        if source["short_url"] in result.content:
            result.content = result.content.replace(
                source["short_url"], source["value"]
            )
            unique_sources.append(source)

    return {
        "messages": [AIMessage(content=result.content)],
        "sources_gathered": unique_sources,
    }


# Create Power Plant Annual Report Search Graph
power_plant_builder = StateGraph(OverallState, config_schema=Configuration)

# Define the power plant specific nodes
power_plant_builder.add_node("generate_power_plant_query", generate_power_plant_query)
power_plant_builder.add_node("power_plant_web_research", power_plant_web_research)
power_plant_builder.add_node("power_plant_reflection", power_plant_reflection)
power_plant_builder.add_node("power_plant_finalize_answer", power_plant_finalize_answer)

# Set the entrypoint as `generate_power_plant_query`
power_plant_builder.add_edge(START, "generate_power_plant_query")

# Add conditional edge to continue with search queries in a parallel branch
power_plant_builder.add_conditional_edges(
    "generate_power_plant_query", continue_to_power_plant_web_research, ["power_plant_web_research"]
)

# Add edge from power_plant_web_research to power_plant_reflection
power_plant_builder.add_edge("power_plant_web_research", "power_plant_reflection")

# Add conditional edge from power_plant_reflection to either continue searching or finalize
power_plant_builder.add_conditional_edges(
    "power_plant_reflection",
    evaluate_power_plant_research,
    ["power_plant_web_research", "power_plant_finalize_answer"]
)

# Add edge from power_plant_finalize_answer to END
power_plant_builder.add_edge("power_plant_finalize_answer", END)

# Compile the power plant graph
power_plant_graph = power_plant_builder.compile(name="power-plant-search-agent")

# Create our Original Agent Graph (kept for reference)
builder = StateGraph(OverallState, config_schema=Configuration)

# Define the nodes we will cycle between
builder.add_node("generate_query", generate_query)
builder.add_node("web_research", web_research)
builder.add_node("reflection", reflection)
builder.add_node("finalize_answer", finalize_answer)

# Set the entrypoint as `generate_query`
# This means that this node is the first one called
builder.add_edge(START, "generate_query")
# Add conditional edge to continue with search queries in a parallel branch
builder.add_conditional_edges(
    "generate_query", continue_to_web_research, ["web_research"]
)
# Reflect on the web research
builder.add_edge("web_research", "reflection")
# Evaluate the research
builder.add_conditional_edges(
    "reflection", evaluate_research, ["web_research", "finalize_answer"]
)
# Finalize the answer
builder.add_edge("finalize_answer", END)

graph = builder.compile(name="pro-search-agent")
